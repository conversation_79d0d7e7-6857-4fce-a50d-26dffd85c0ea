import React, { useState } from "react";
import { FaTimes } from "react-icons/fa";
import "../../styles/RequestCustomTrainingModal.css";

const RequestCustomTrainingModal = ({ isOpen, onClose, strategy }) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    message: ""
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission logic here
    console.log("Custom training request submitted:", formData);
    // Reset form and close modal
    setFormData({
      name: "",
      email: "",
      phone: "",
      message: ""
    });
    onClose();
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="request-modal-overlay" onClick={handleOverlayClick}>
      <div className="request-modal">
        <div className="request-modal__header">
          <h2 className="request-modal__title">Request Custom Training</h2>
          <button className="request-modal__close" onClick={onClose}>
            <FaTimes />
          </button>
        </div>

        <div className="request-modal__content">
          <form onSubmit={handleSubmit} className="request-modal__form">
            <div className="request-modal__form-group">
              <label htmlFor="name" className="request-modal__label">
                Full Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="request-modal__input"
                placeholder="Enter your full name"
                required
              />
            </div>

            <div className="request-modal__form-group">
              <label htmlFor="email" className="request-modal__label">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="request-modal__input"
                placeholder="Enter your email address"
                required
              />
            </div>

            <div className="request-modal__form-group">
              <label htmlFor="phone" className="request-modal__label">
                Phone Number
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="request-modal__input"
                placeholder="Enter your phone number"
                required
              />
            </div>

            <div className="request-modal__form-group">
              <label htmlFor="message" className="request-modal__label">
                Message
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                className="request-modal__textarea"
                placeholder="Tell us about your custom training requirements..."
                rows="4"
                required
              />
            </div>

            <button type="submit" className="request-modal__submit-btn">
              Send Request
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default RequestCustomTrainingModal;

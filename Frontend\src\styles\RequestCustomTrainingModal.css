/* Request Custom Training Modal Styles */
.request-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: var(--heading5);
}

.request-modal {
  background: var(--white);
  border-radius: var(--border-radius-large);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--box-shadow-dark);
  animation: modalSlideIn 0.3s ease-out;
  position: relative;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Header */
.request-modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--heading5) var(--heading5) 0;
  border-bottom: 1px solid var(--light-gray);
  margin-bottom: var(--heading5);
}

.request-modal__title {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0;
  text-align: center;
  flex: 1;
}

.request-modal__close {
  background: none;
  border: none;
  font-size: var(--heading6);
  color: var(--dark-gray);
  cursor: pointer;
  padding: var(--border-radius-medium);
  border-radius: var(--border-radius);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.request-modal__close:hover {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

/* Modal Content */
.request-modal__content {
  padding: 0 var(--heading5) var(--heading5);
}

/* Form Styles */
.request-modal__form {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.request-modal__form-group {
  display: flex;
  flex-direction: column;
  gap: var(--border-radius-medium);
}

.request-modal__label {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--secondary-color);
  margin: 0;
}

.request-modal__input,
.request-modal__textarea {
  width: 100%;
  padding: var(--extrasmallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  font-size: var(--smallfont);
  color: var(--text-color);
  background-color: var(--white);
  transition: all 0.2s ease;
  font-family: inherit;
  box-sizing: border-box;
}

.request-modal__input:focus,
.request-modal__textarea:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.request-modal__input::placeholder,
.request-modal__textarea::placeholder {
  color: var(--dark-gray);
}

.request-modal__textarea {
  resize: vertical;
  min-height: 100px;
}

/* Submit Button */
.request-modal__submit-btn {
  background: linear-gradient(to bottom, var(--btn-color), #fb5024);
  color: var(--white);
  padding: var(--smallfont) var(--heading4);
  border-radius: var(--border-radius-medium);
  font-weight: 600;
  font-size: var(--basefont);
  text-align: center;
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: var(--basefont);
}

.request-modal__submit-btn:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(238, 52, 37, 0.4);
}

.request-modal__submit-btn:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .request-modal {
    margin: var(--heading5);
    max-width: calc(100vw - 40px);
  }

  .request-modal__header,
  .request-modal__content {
    padding-left: var(--basefont);
    padding-right: var(--basefont);
  }

  .request-modal__title {
    font-size: var(--heading6);
  }

  .request-modal__input,
  .request-modal__textarea {
    padding: var(--extrasmallfont) var(--smallfont);
  }
}

@media (max-width: 480px) {
  .request-modal-overlay {
    padding: var(--extrasmallfont);
  }

  .request-modal {
    margin: var(--extrasmallfont);
    max-width: calc(100vw - 24px);
  }

  .request-modal__header,
  .request-modal__content {
    padding-left: var(--extrasmallfont);
    padding-right: var(--extrasmallfont);
  }

  .request-modal__submit-btn {
    padding: var(--extrasmallfont) var(--heading5);
  }
}
